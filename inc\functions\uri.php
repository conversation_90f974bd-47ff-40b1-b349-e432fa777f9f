<?php

global $current_uri;
global $current_uri_full;
global $q_string;
global $current_uri_no_q;
global $current_uri_no_q_or_lang;
global $domain_prefix;
global $domain;
global $is_site_local;

$current_uri = strtolower($_SERVER['REQUEST_URI']);
$current_uri_full = strtolower($_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']);
if (isset($_SERVER['QUERY_STRING']) && !empty($_SERVER['QUERY_STRING'])) $q_string = '?' . strtolower($_SERVER['QUERY_STRING']);
$current_uri_no_q = str_replace($q_string, '', $current_uri);
$current_uri_no_q_or_lang = substr($current_uri_no_q, 3);

if (isset($_SERVER['HTTPS'])) {
	if ($_SERVER['HTTPS'] == 'on') $domain_prefix = 'https://';
}
else $domain_prefix = 'http://';

$domain = $_SERVER['HTTP_HOST'];

if ($_SERVER['HTTP_HOST'] == 'www.h-store.com.ua') $is_site_local = false;
// else if ($_SERVER['HTTP_HOST'] == '***********' || $_SERVER['HTTP_HOST'] == 'h-store.test') $is_site_local = true;
else $is_site_local = true;

?>
