<?php

// Global variables for URI and domain handling
global $current_uri;              // Full URI with query string (e.g., "/en/shop/fern?param=123")
global $current_uri_full;         // Domain + URI (e.g., "h-store.test/en/shop/fern?param=123")
global $q_string;                 // Query string part only (e.g., "?param=123")
global $current_uri_no_q;         // URI without query string (e.g., "/en/shop/fern")
global $current_uri_no_q_or_lang; // URI without query string and language prefix (e.g., "/shop/fern")
global $domain_prefix;            // Protocol prefix (http:// or https://)
global $domain;                   // Domain name without protocol (e.g., "h-store.test")
global $is_site_local;            // Boolean flag to determine if site is running locally

// Get the current URI path and convert to lowercase
$current_uri = strtolower($_SERVER['REQUEST_URI']);

// Combine server name with URI for full path
$current_uri_full = strtolower($_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']);

// Extract query string if it exists and add question mark prefix
if (isset($_SERVER['QUERY_STRING']) && !empty($_SERVER['QUERY_STRING'])) {
    $q_string = '?' . strtolower($_SERVER['QUERY_STRING']);
}

// Remove query string from URI to get clean path
$current_uri_no_q = str_replace($q_string, '', $current_uri);

// Remove language prefix (first 3 characters: "/en" or "/uk") from URI
$current_uri_no_q_or_lang = substr($current_uri_no_q, 3);

// Determine protocol (HTTP or HTTPS)
if (isset($_SERVER['HTTPS'])) {
    // If HTTPS is set and enabled, use HTTPS protocol
    if ($_SERVER['HTTPS'] == 'on') $domain_prefix = 'https://';
}
else {
    // Default to HTTP if HTTPS is not set
    $domain_prefix = 'http://';
}

// Get the domain name from HTTP_HOST
$domain = $_SERVER['HTTP_HOST'];

// Determine if site is running on production or local environment
if ($_SERVER['HTTP_HOST'] == 'www.h-store.com.ua') {
    // Production site
    $is_site_local = false;
}
// else if ($_SERVER['HTTP_HOST'] == '***********' || $_SERVER['HTTP_HOST'] == 'h-store.test') $is_site_local = true;
else {
    // Local development environment (any domain other than production)
    $is_site_local = true;
}

?>
