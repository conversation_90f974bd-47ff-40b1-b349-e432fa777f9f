<?php

/**
 * Language Detection and Locale Management
 *
 * This file handles multilingual functionality for the H-Store theme.
 * It detects the current language from URL paths, manages language cookies,
 * and sets up WordPress locale settings for proper internationalization.
 *
 * Language detection priority:
 * 1. URL path prefix (/en, /uk, /ru)
 * 2. Language cookie if no URL prefix found
 * 3. Default to Ukrainian if neither URL nor cookie available
 *
 * Supported languages: Ukrainian (default), English, Russian
 */

// Global variables for language and locale handling
global $lang_arr;							// Array of available languages with display names
global $locale_names_array;		// WordPress locale codes for each language
global $current_uri_no_q;			// Current URI without query string (from uri.php)
global $current_lang;					// Current language code (uk, en, ru)
global $current_locale;				// WordPress locale code for current language

// Note: $locale is a WordPress global variable

// Available languages with their display names
$lang_arr = [
	'uk' => 'Українська',		// Ukrainian (default language)
	'en' => 'English',			// English
	// 'ru' => '🐽🐶'
	'ru' => 'Русский'				// Russian
];

// WordPress locale codes corresponding to each language
$locale_names_array = [
	'uk' => 'uk_UA',			// Ukrainian locale
	'en' => 'en',					// English locale (default WordPress)
	'ru' => 'ru_UA'				// Russian locale for Ukraine
];

// Primary language detection: Check URL path for language prefix
foreach ($lang_arr as $lang_key => $lang_value) {
	// Check if URI starts with language code (e.g., /en, /uk, /ru)
	if (preg_match("/^\/{$lang_key}/i", $current_uri_no_q)) {
		$current_lang = $lang_key;																	// Set current language
		$current_locale = $locale = $locale_names_array[$lang_key];	// Set WordPress locale
		setcookie('lang', $lang_key, time() + 3600, '/', $domain);	// Save language preference in cookie (1 hour)
	}
}

// Fallback language detection: If no language found in URL
if (!isset($current_locale)) {
	// Check if language cookie exists
	if (isset($_COOKIE['lang'])) {
		$cookie = $_COOKIE['lang'];
		// Validate cookie value against available languages
		foreach ($lang_arr as $lang_key => $lang_value) {
			if ($cookie == $lang_key) {
				$current_lang = $lang_key;																	// Use language from cookie
				$current_locale = $locale = $locale_names_array[$lang_key];	// Set corresponding locale
			}
		}
	}
	else {
		// Default fallback: Use Ukrainian as default language
		$current_lang = 'uk';
		$current_locale = $locale = $locale_names_array[$current_lang];
	}
}

?>
