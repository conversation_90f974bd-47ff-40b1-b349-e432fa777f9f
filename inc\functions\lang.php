<?php

global $lang_arr;
global $locale_names_array;
global $current_uri_no_q;
global $current_lang;
global $current_locale;

// $locale – вордпресівський глобал

$lang_arr = [
	'uk' => 'Українська',
	'en' => 'English',
	// 'ru' => '🐽🐶'
	'ru' => 'Русский'
];

$locale_names_array = [
	'uk' => 'uk_UA',
	'en' => 'en',
	'ru' => 'ru_UA'
];

foreach ($lang_arr as $lang_key => $lang_value) {
	if (preg_match("/^\/{$lang_key}/i", $current_uri_no_q)) {
		$current_lang = $lang_key;
		$current_locale = $locale = $locale_names_array[$lang_key];
		setcookie('lang', $lang_key, time() + 3600, '/', $domain);
	}
}

if (!isset($current_locale)) {
	if (isset($_COOKIE['lang'])) {
		$cookie = $_COOKIE['lang'];
		foreach ($lang_arr as $lang_key => $lang_value) {
			if ($cookie == $lang_key) {
				$current_lang = $lang_key;
				$current_locale = $locale = $locale_names_array[$lang_key];
			}
		}
	}
	else {
		$current_lang = 'uk';
		$current_locale = $locale = $locale_names_array[$current_lang];
	}
}

?>
