# H-Store WordPress Theme

A custom WordPress theme for an e-commerce furniture store specializing in office chairs and furniture. This theme provides a complete online shopping experience with multilingual support, custom product management, and integrated order processing.

## 🛋️ About

H-Store is a specialized e-commerce theme designed for selling office furniture, particularly chairs. The theme features a clean, modern design with focus on product presentation and user experience. It includes custom post types for product management, shopping cart functionality, and order processing.

## ✨ Features

### 🛒 E-commerce Functionality
- Custom shopping cart with AJAX functionality
- Product catalog with detailed product pages
- Stock management and availability tracking
- Order processing with email notifications
- Price formatting in Ukrainian Hryvnia (₴)

### 🌍 Multilingual Support
- Built-in language switching (English/Ukrainian)
- Localized content management
- Language-specific URLs and routing
- Multilingual product descriptions and metadata

### 📱 Responsive Design
- Mobile-first responsive layout
- Optimized for all device sizes
- Touch-friendly interface elements

### 🎨 Custom Features
- Custom post types for products and settings
- Advanced metaboxes for product management
- Custom admin interface for shop management
- Automated build process with Gulp
- SCSS compilation and CSS optimization

## 🏗️ Technical Stack

- **WordPress**: Custom theme development
- **PHP**: Server-side logic and WordPress integration
- **JavaScript**: Interactive features and AJAX functionality
- **SCSS/CSS**: Styling with modern CSS features
- **Gulp**: Build automation and asset optimization
- **Node.js**: Development dependencies and build tools

## 📁 Project Structure

```
h-store/
├── css/                    # Compiled CSS files
├── dev/scss/              # SCSS source files
├── inc/functions/         # PHP functionality modules
├── js/                    # JavaScript files
├── shop-template-parts/   # Shop-specific template parts
├── slick/                 # Slick carousel library
├── _prod/                 # Production build output
├── functions.php          # Main theme functions
├── gulpfile.js           # Build configuration
└── package.json          # Node.js dependencies
```

### Key Files

- `functions.php` - Main theme setup and module loading
- `page-shop.php` - Shop catalog page template
- `single-shop-*.php` - Individual product page templates
- `inc/functions/shop.php` - E-commerce functionality
- `inc/functions/posts.php` - Custom post type definitions
- `gulpfile.js` - Build automation configuration

## 🚀 Installation

1. **Clone or download** the theme to your WordPress themes directory:
   ```
   wp-content/themes/h-store/
   ```

2. **Install Node.js dependencies**:
   ```bash
   npm install
   ```

3. **Activate the theme** in WordPress admin panel

4. **Configure shop settings** through the WordPress admin interface

## 🛠️ Development

### Build Process

The theme uses Gulp for automated build processes:

```bash
# Development mode (watch for changes)
npm run dev
# or
gulp watch

# Production build
gulp prod

# Compile SCSS only
gulp sass

# Clean build directories
gulp clean
```

### Available Gulp Tasks

- `gulp sass` - Compile SCSS to CSS (development)
- `gulp minifySass` - Compile and compress SCSS
- `gulp nano` - Compile SCSS with advanced optimization
- `gulp minifyJs` - Minify JavaScript files
- `gulp php` - Copy PHP files to production
- `gulp prod` - Full production build
- `gulp watch` - Watch for changes and auto-compile
- `gulp clean` - Clean all build directories

### SCSS Development

SCSS files are located in `dev/scss/` and compiled to `css/` directory. The build process includes:

- Autoprefixer for browser compatibility
- CSS optimization and minification
- Source map generation for debugging

## 🛒 Shop Features

### Product Management
- Custom post type for products
- Product categories and attributes
- Stock quantity tracking
- Price management
- Product image galleries
- Multilingual product descriptions

### Shopping Cart
- AJAX-powered cart functionality
- Real-time stock validation
- Persistent cart using cookies
- Cart quantity management

### Order Processing
- Customer information collection
- Order number generation
- Email notifications to customers and admin
- Order history and management

### Supported Products
The theme is configured for specific furniture products:
- Fern chairs
- Soji chairs (Black and White variants)
- Fern Plus and Fern X Halo models

## 🌐 Multilingual Setup

The theme supports English and Ukrainian languages with:
- Language-specific content fields
- Automatic URL routing based on language
- Localized shop settings and messages
- Currency formatting for Ukrainian market

## 📧 Email Integration

- Automated order confirmation emails
- HTML email templates
- Admin notification system
- Customer communication features

## 🔧 Configuration

### Shop Settings
Configure shop-specific settings through WordPress admin:
- Product availability messages
- Email templates
- Currency formatting
- Stock management options

### Database
The theme creates custom database tables for:
- Order management
- Customer information
- Product tracking

## 📱 Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for all screen sizes

## 🤝 Contributing

This is a custom theme for a specific business. For modifications or enhancements:

1. Follow WordPress coding standards
2. Test thoroughly on different devices
3. Maintain multilingual compatibility
4. Ensure e-commerce functionality remains intact

## 📄 License

Custom proprietary theme for H-Store. All rights reserved.

## 📞 Support

For technical support or customization requests, contact the development team.

---

**Note**: This theme is specifically designed for H-Store's business requirements and may need customization for other use cases.