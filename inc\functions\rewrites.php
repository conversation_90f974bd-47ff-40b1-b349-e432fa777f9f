<?php

global $current_uri_no_q;
global $current_lang;
global $q_string;
global $sitemap_arr;
global $sitemap_names_arr;

if ($current_uri_no_q == '/') {
	wp_redirect('/' . $current_lang . $q_string);
	exit;
}

$redirect_arr = ['/index.php', '/functions.php', '/header.php', '/footer.php'];

if (in_array($current_uri_no_q, $redirect_arr)) {
	wp_redirect('/' . $current_lang);
	exit;
}

$redirect_uri_no_q = '';

if (preg_match('/^\/shop$/', $current_uri_no_q)) $redirect_uri_no_q = '/shop';
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*|(?:\/shop)*)\/fern$/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/fern';
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*|(?:\/shop)*)\/fern-plus$/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/fern-plus';
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*|(?:\/shop)*)\/fern-x-halo$/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/fern-x-halo';
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*|(?:\/shop)*)\/(?:soji$|soji-black$)/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/soji-black';
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*|(?:\/shop)*)\/soji-white$/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/soji-white';
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*(?:\/shop)*)\/(?:soji$)/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/soji-black';

// (?:) -- checks but forgets group, won't search for /en, only /en/shop/fern;
// ^ -- beginning of string, won't search for /asd/ru/shop/fern
// $ -- end of string, won't search for /ru/shop/fernasdasd
// * -- 0 or more instances of what's before
// /i -- case insensitive
// regex101 -- all explained

if (!empty($redirect_uri_no_q)) {
  wp_redirect('/' . $current_lang . $redirect_uri_no_q . $q_string);
  exit;
}

$sitemap_arr = [];
$sitemap_arr['home'] = [];
$sitemap_arr['shop'] = ['fern', 'fern-plus', 'fern-x-halo', 'soji-black', 'soji-white'];
$sitemap_names_arr = ['home' => '', 'shop' => 'shop'];

function sitemap_redirect_func() {
  include_once(get_template_directory() . '/sitemap.php' );
  exit;
}

if (preg_match('/^(?:\/sitemap|\/sitemap\.xml|\/wp-sitemap.xml)$/i', $current_uri_no_q)) add_action('parse_request', 'sitemap_redirect_func');


// WP rewrites


function permalinks_rewrite_func() {
  global $lang_arr, $pages_array;

  add_rewrite_rule('(?i)^ru$', ['pagename' => 'shop'], 'top');
  add_rewrite_rule('(?i)^en$', ['pagename' => 'shop'], 'top');
  add_rewrite_rule('(?i)^uk$', ['pagename' => 'shop'], 'top');

  add_rewrite_rule('(?i)^ru/shop$', ['pagename' => 'shop'], 'top');
  add_rewrite_rule('(?i)^en/shop$', ['pagename' => 'shop'], 'top');
  add_rewrite_rule('(?i)^uk/shop$', ['pagename' => 'shop'], 'top');

  add_rewrite_rule('(?i)^hwtest$', ['pagename' => 'hwtest'], 'top');

}

add_action('init', 'permalinks_rewrite_func');


function page_rewrite_rules_func($rules) {

  // global $lang_arr;
  // $new_rules = $rules;

  // foreach ($rules as $rule => $rewrite) {

    // foreach ($lang_arr as $lang => $lang_name) {
      // $new_rule = $lang . '/' . $rule;
      // $new_rules[$new_rule] = $rewrite;
    // }

    // unset($new_rules[$rule]);

  // }

  // return $new_rules;

  // foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('page_rewrite_rules', 'page_rewrite_rules_func');


function post_rewrite_rules_func($rules) {
  foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('post_rewrite_rules', 'post_rewrite_rules_func');


function author_rewrite_rules_func($rules) {
  foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('author_rewrite_rules', 'author_rewrite_rules_func');


function tag_rewrite_rules_func($rules) {
  foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('tag_rewrite_rules', 'tag_rewrite_rules_func');


function date_rewrite_rules_func($rules) {
  foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('date_rewrite_rules', 'date_rewrite_rules_func');


function root_rewrite_rules_func($rules) {
  foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('root_rewrite_rules', 'root_rewrite_rules_func');


function comments_rewrite_rules_func($rules) {
  foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('comments_rewrite_rules', 'comments_rewrite_rules_func');


function search_rewrite_rules_func($rules) {
  foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('search_rewrite_rules', 'search_rewrite_rules_func');


function category_rewrite_rules_func($rules) {
  foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('category_rewrite_rules', 'category_rewrite_rules_func');


function post_tag_rewrite_rules_func($rules) {
  foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('post_tag_rewrite_rules', 'post_tag_rewrite_rules_func');


function post_format_rewrite_rules_func($rules) {
  foreach ($rules as $rule => $rewrite) unset($rules[$rule]);
  return $rules;
}

add_filter('post_format_rewrite_rule1s', 'post_format_rewrite_rules_func');


function shop_rewrite_rules_func($rules) {

  global $lang_arr;
  $new_rules = $rules;

  foreach ($rules as $rule => $rewrite) {

    foreach ($lang_arr as $lang => $lang_name) {
      $new_rule = '(?i)' . $lang . '/' . $rule;
      $new_rules[$new_rule] = $rewrite;
    }

    unset($new_rules[$rule]);

  }

  return $new_rules;

}

add_filter('shop_rewrite_rules', 'shop_rewrite_rules_func');


remove_filter('template_redirect', 'redirect_canonical');


function remove_sitemap_providers_func($provider, $name) {

  if ('users' === $name || 'taxonomies' === $name || 'posts' === $name) return false;

  return $provider;

}

add_filter('wp_sitemaps_add_provider', 'remove_sitemap_providers_func', 10, 2);


$wp_rewrite->flush_rules(); // !!! remove in prod


?>
