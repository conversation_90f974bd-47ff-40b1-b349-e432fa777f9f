<?php

/**
 * URL Rewrites and Redirects Management
 *
 * This file handles URL rewriting, redirects, and routing for the H-Store multilingual theme.
 * It manages clean URLs, product redirects, sitemap generation, and WordPress rewrite rules.
 *
 * Key functions:
 * - Redirects root URL to language-specific home page
 * - Handles product URL redirects and normalization
 * - Manages sitemap routing and generation
 * - Sets up custom WordPress rewrite rules for multilingual support
 * - Disables unnecessary WordPress URL patterns
 * - Ensures clean, SEO-friendly URLs across all languages
 */

// Global variables from other modules
global $current_uri_no_q;		// Current URI without query string (from uri.php)
global $current_lang;				// Current language code (from lang.php)
global $q_string;						// Query string part (from uri.php)
global $sitemap_arr;				// Sitemap structure array
global $sitemap_names_arr;	// Sitemap page names mapping

// Redirect root URL to language-specific home page
// Example: "/" redirects to "/uk" or "/en" based on detected language
if ($current_uri_no_q == '/') {
	wp_redirect('/' . $current_lang . $q_string);
	exit;
}

// Block direct access to PHP files - security measure
// Prevents users from accessing theme files directly
$redirect_arr = ['/index.php', '/functions.php', '/header.php', '/footer.php'];

if (in_array($current_uri_no_q, $redirect_arr)) {
	wp_redirect('/' . $current_lang);	// Redirect to language home page
	exit;
}

// Product URL normalization and redirects
// Handles various product URL patterns and redirects them to canonical URLs
$redirect_uri_no_q = '';

// Shop page redirect: /shop -> /lang/shop
if (preg_match('/^\/shop$/', $current_uri_no_q)) $redirect_uri_no_q = '/shop';

// Fern product redirects: various patterns -> /shop/fern
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*|(?:\/shop)*)\/fern$/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/fern';

// Fern Plus product redirects: -> /shop/fern-plus
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*|(?:\/shop)*)\/fern-plus$/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/fern-plus';

// Fern X Halo product redirects: -> /shop/fern-x-halo
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*|(?:\/shop)*)\/fern-x-halo$/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/fern-x-halo';

// Soji Black product redirects: /soji or /soji-black -> /shop/soji-black
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*|(?:\/shop)*)\/(?:soji$|soji-black$)/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/soji-black';

// Soji White product redirects: -> /shop/soji-white
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*|(?:\/shop)*)\/soji-white$/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/soji-white';

// Additional Soji redirect (fallback): /soji -> /shop/soji-black
elseif (preg_match('/^(?:(?:\/en|\/ru|\/uk)*(?:\/shop)*)\/(?:soji$)/i', $current_uri_no_q)) $redirect_uri_no_q = '/shop/soji-black';

// Regex explanation for reference:
// (?:) -- Non-capturing group, checks pattern but doesn't store it
// ^ -- Beginning of string anchor, prevents matching partial URLs
// $ -- End of string anchor, prevents matching URLs with extra content
// * -- Zero or more instances of the preceding pattern
// /i -- Case insensitive flag
// For testing and explanation: regex101.com

// Execute redirect if a canonical URL was determined
if (!empty($redirect_uri_no_q)) {
	wp_redirect('/' . $current_lang . $redirect_uri_no_q . $q_string);	// Redirect with language prefix and query string
	exit;
}

// Sitemap structure definition
// Define the site structure for XML sitemap generation
$sitemap_arr = [];
$sitemap_arr['home'] = [];													// Home page (no sub-pages)
$sitemap_arr['shop'] = ['fern', 'fern-plus', 'fern-x-halo', 'soji-black', 'soji-white'];	// Shop page with product sub-pages
$sitemap_names_arr = ['home' => '', 'shop' => 'shop'];						// URL mapping for pages

// Sitemap generation function
// Includes the custom sitemap.php file and terminates execution
function sitemap_redirect_func() {
	include_once(get_template_directory() . '/sitemap.php');	// Load custom sitemap generator
	exit;														// Stop further processing
}

// Sitemap URL pattern matching and redirect
// Handles various sitemap URL patterns: /sitemap, /sitemap.xml, /wp-sitemap.xml
if (preg_match('/^(?:\/sitemap|\/sitemap\.xml|\/wp-sitemap.xml)$/i', $current_uri_no_q)) {
	add_action('parse_request', 'sitemap_redirect_func');		// Hook sitemap generation to WordPress parse_request
}


// WordPress Custom Rewrite Rules
// Set up custom URL patterns for multilingual pages

function permalinks_rewrite_func() {
	global $lang_arr, $pages_array;	// Global variables (unused but kept for potential future use)

	// Language-only URLs redirect to shop page
	// Examples: /ru -> shop page, /en -> shop page, /uk -> shop page
	add_rewrite_rule('(?i)^ru$', ['pagename' => 'shop'], 'top');
	add_rewrite_rule('(?i)^en$', ['pagename' => 'shop'], 'top');
	add_rewrite_rule('(?i)^uk$', ['pagename' => 'shop'], 'top');

	// Language + shop URLs map to shop page
	// Examples: /ru/shop -> shop page, /en/shop -> shop page, /uk/shop -> shop page
	add_rewrite_rule('(?i)^ru/shop$', ['pagename' => 'shop'], 'top');
	add_rewrite_rule('(?i)^en/shop$', ['pagename' => 'shop'], 'top');
	add_rewrite_rule('(?i)^uk/shop$', ['pagename' => 'shop'], 'top');

	// Hardware test page (development/testing page)
	add_rewrite_rule('(?i)^hwtest$', ['pagename' => 'hwtest'], 'top');
}

// Hook the rewrite rules to WordPress initialization
add_action('init', 'permalinks_rewrite_func');


// Disable WordPress Default Rewrite Rules
// These functions remove default WordPress URL patterns that are not needed for this theme
// This helps prevent conflicts with custom multilingual routing and improves security

function page_rewrite_rules_func($rules) {
	// Page rewrite rules - currently preserved but could be modified for multilingual support
	// Commented code shows how to add language prefixes to page rules if needed in future

	// global $lang_arr;
	// $new_rules = $rules;
	// foreach ($rules as $rule => $rewrite) {
	//     foreach ($lang_arr as $lang => $lang_name) {
	//         $new_rule = $lang . '/' . $rule;
	//         $new_rules[$new_rule] = $rewrite;
	//     }
	//     unset($new_rules[$rule]);
	// }
	// return $new_rules;

	return $rules;	// Keep default page rules for now
}
add_filter('page_rewrite_rules', 'page_rewrite_rules_func');

// Disable post-related URL patterns (not used in this e-commerce theme)
function post_rewrite_rules_func($rules) {
	foreach ($rules as $rule => $rewrite) unset($rules[$rule]);	// Remove all post rewrite rules
	return $rules;
}
add_filter('post_rewrite_rules', 'post_rewrite_rules_func');

// Disable author pages (not needed for product catalog)
function author_rewrite_rules_func($rules) {
	foreach ($rules as $rule => $rewrite) unset($rules[$rule]);	// Remove all author rewrite rules
	return $rules;
}
add_filter('author_rewrite_rules', 'author_rewrite_rules_func');

// Disable tag pages (not using WordPress tags)
function tag_rewrite_rules_func($rules) {
	foreach ($rules as $rule => $rewrite) unset($rules[$rule]);	// Remove all tag rewrite rules
	return $rules;
}
add_filter('tag_rewrite_rules', 'tag_rewrite_rules_func');

// Disable date-based archives (not relevant for product catalog)
function date_rewrite_rules_func($rules) {
	foreach ($rules as $rule => $rewrite) unset($rules[$rule]);	// Remove all date rewrite rules
	return $rules;
}
add_filter('date_rewrite_rules', 'date_rewrite_rules_func');

// Disable root-level WordPress URLs (using custom routing)
function root_rewrite_rules_func($rules) {
	foreach ($rules as $rule => $rewrite) unset($rules[$rule]);	// Remove all root rewrite rules
	return $rules;
}
add_filter('root_rewrite_rules', 'root_rewrite_rules_func');

// Disable comment feeds and pages (comments not used)
function comments_rewrite_rules_func($rules) {
	foreach ($rules as $rule => $rewrite) unset($rules[$rule]);	// Remove all comment rewrite rules
	return $rules;
}
add_filter('comments_rewrite_rules', 'comments_rewrite_rules_func');

// Disable search functionality (not implemented in this theme)
function search_rewrite_rules_func($rules) {
	foreach ($rules as $rule => $rewrite) unset($rules[$rule]);	// Remove all search rewrite rules
	return $rules;
}
add_filter('search_rewrite_rules', 'search_rewrite_rules_func');

// Disable category pages (using custom product organization)
function category_rewrite_rules_func($rules) {
	foreach ($rules as $rule => $rewrite) unset($rules[$rule]);	// Remove all category rewrite rules
	return $rules;
}
add_filter('category_rewrite_rules', 'category_rewrite_rules_func');

// Disable post tag pages (not using WordPress post tags)
function post_tag_rewrite_rules_func($rules) {
	foreach ($rules as $rule => $rewrite) unset($rules[$rule]);	// Remove all post tag rewrite rules
	return $rules;
}
add_filter('post_tag_rewrite_rules', 'post_tag_rewrite_rules_func');

// Disable post format pages (not using WordPress post formats)
function post_format_rewrite_rules_func($rules) {
	foreach ($rules as $rule => $rewrite) unset($rules[$rule]);	// Remove all post format rewrite rules
	return $rules;
}
add_filter('post_format_rewrite_rule1s', 'post_format_rewrite_rules_func');	// Note: typo in filter name (rule1s instead of rules)


// Enable multilingual support for shop (custom post type) URLs
function shop_rewrite_rules_func($rules) {
	global $lang_arr;
	$new_rules = $rules;

	// Add language prefixes to all shop rewrite rules
	foreach ($rules as $rule => $rewrite) {
		// Create new rules with language prefixes for each available language
		foreach ($lang_arr as $lang => $lang_name) {
			$new_rule = '(?i)' . $lang . '/' . $rule;	// Add language prefix with case-insensitive flag
			$new_rules[$new_rule] = $rewrite;			// Add new rule to array
		}

		unset($new_rules[$rule]);	// Remove original rule without language prefix
	}

	return $new_rules;
}
add_filter('shop_rewrite_rules', 'shop_rewrite_rules_func');

// Disable WordPress canonical URL redirects
// Prevents conflicts with custom multilingual URL handling
remove_filter('template_redirect', 'redirect_canonical');

// Remove unnecessary WordPress sitemap providers
// Keeps only the custom sitemap functionality
function remove_sitemap_providers_func($provider, $name) {
	// Disable default WordPress sitemaps for users, taxonomies, and posts
	if ('users' === $name || 'taxonomies' === $name || 'posts' === $name) {
		return false;	// Remove these sitemap providers
	}

	return $provider;	// Keep other providers
}
add_filter('wp_sitemaps_add_provider', 'remove_sitemap_providers_func', 10, 2);

// Force flush rewrite rules (DEVELOPMENT ONLY)
// WARNING: Remove this line in production for performance
$wp_rewrite->flush_rules(); // !!! remove in prod

?>
